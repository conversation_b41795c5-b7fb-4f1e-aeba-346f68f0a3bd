import express from 'express';
import { Request, Response } from 'express';
import { authMiddleware } from '../../../middleware/authMiddleware';
import { asyncHandler } from '../../../middleware/errorHandler';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const router = express.Router();

/**
 * @route PUT /api/admin/documents/:documentId/status
 * @desc Update document verification status
 * @access Admin only
 */
// Admin middleware - check if user is admin
const adminMiddleware = (req: any, res: any, next: any) => {
  if (!req.user || (req.user.userType !== 'ADMIN' && req.user.userType !== 'admin')) {
    return res.status(403).json({
      success: false,
      error: 'Admin access required'
    });
  }
  next();
};

router.put('/:documentId/status',
  authMiddleware,
  adminMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { documentId } = req.params;
    const { status, adminId } = req.body;

    console.log('Document status update request:', {
      documentId,
      status,
      adminId
    });

    try {
      // Document ID'den user ID'yi çıkar
      // Format: "tax_cert_userId" veya "trade_reg_userId" gibi
      const userIdMatch = documentId.match(/_([^_]+)$/);
      if (!userIdMatch) {
        return res.status(400).json({
          success: false,
          error: 'Invalid document ID format'
        });
      }

      const userId = userIdMatch[1];
      console.log('Extracted user ID:', userId);

      // Kullanıcıyı bul
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { profile: true }
      });

      if (!user || !user.profile) {
        return res.status(404).json({
          success: false,
          error: 'User or profile not found'
        });
      }

      // Document type'ı belirle
      let documentType = '';
      if (documentId.includes('tax_cert')) {
        documentType = 'taxCertificate';
      } else if (documentId.includes('trade_reg')) {
        documentType = 'tradeRegistry';
      } else if (documentId.includes('mining_license')) {
        documentType = 'miningLicense';
      } else if (documentId.includes('capacity_report')) {
        documentType = 'capacityReport';
      } else if (documentId.includes('industry_reg')) {
        documentType = 'industryRegistry';
      } else {
        return res.status(400).json({
          success: false,
          error: 'Unknown document type'
        });
      }

      console.log('Document type:', documentType);

      // Mevcut verification documents'ı al
      const currentDocs = user.profile.verificationDocuments as any || {};
      
      // Document status'unu güncelle
      const updatedDocs = {
        ...currentDocs,
        [`${documentType}Status`]: status,
        [`${documentType}VerifiedAt`]: status === 'verified' ? new Date() : null,
        [`${documentType}VerifiedBy`]: status === 'verified' ? adminId : null
      };

      console.log('Updated documents:', updatedDocs);

      // Profile'ı güncelle
      await prisma.userProfile.update({
        where: { userId: userId },
        data: {
          verificationDocuments: updatedDocs
        }
      });

      console.log('Document status updated successfully');

      res.json({
        success: true,
        message: `Document ${status} successfully`,
        data: {
          documentId,
          documentType,
          status,
          verifiedAt: status === 'verified' ? new Date() : null
        }
      });

    } catch (error) {
      console.error('Error updating document status:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  })
);

/**
 * @route GET /api/admin/documents/:userId
 * @desc Get all documents for a user
 * @access Admin only
 */
router.get('/:userId',
  authMiddleware,
  adminMiddleware,
  asyncHandler(async (req: Request, res: Response) => {
    const { userId } = req.params;

    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { profile: true }
      });

      if (!user || !user.profile) {
        return res.status(404).json({
          success: false,
          error: 'User or profile not found'
        });
      }

      const docs = user.profile.verificationDocuments as any || {};
      
      const documents = [
        {
          id: `tax_cert_${userId}`,
          type: 'tax_certificate',
          url: docs.taxCertificate,
          status: docs.taxCertificateStatus || 'pending',
          verifiedAt: docs.taxCertificateVerifiedAt,
          verifiedBy: docs.taxCertificateVerifiedBy
        },
        {
          id: `trade_reg_${userId}`,
          type: 'trade_registry',
          url: docs.tradeRegistry,
          status: docs.tradeRegistryStatus || 'pending',
          verifiedAt: docs.tradeRegistryVerifiedAt,
          verifiedBy: docs.tradeRegistryVerifiedBy
        },
        {
          id: `capacity_report_${userId}`,
          type: 'capacity_report',
          url: docs.capacityReport,
          status: docs.capacityReportStatus || 'pending',
          verifiedAt: docs.capacityReportVerifiedAt,
          verifiedBy: docs.capacityReportVerifiedBy
        },
        {
          id: `industry_reg_${userId}`,
          type: 'industry_registry',
          url: docs.industryRegistry,
          status: docs.industryRegistryStatus || 'pending',
          verifiedAt: docs.industryRegistryVerifiedAt,
          verifiedBy: docs.industryRegistryVerifiedBy
        }
      ];

      // Ocak sahibi ise mining license ekle
      if (user.profile.hasQuarry) {
        documents.push({
          id: `mining_license_${userId}`,
          type: 'mining_license',
          url: docs.miningLicense,
          status: docs.miningLicenseStatus || 'pending',
          verifiedAt: docs.miningLicenseVerifiedAt,
          verifiedBy: docs.miningLicenseVerifiedBy
        });
      }

      // Sadece URL'i olan belgeleri döndür
      const validDocuments = documents.filter(doc => doc.url);

      res.json({
        success: true,
        data: validDocuments
      });

    } catch (error) {
      console.error('Error fetching documents:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  })
);

export default router;
