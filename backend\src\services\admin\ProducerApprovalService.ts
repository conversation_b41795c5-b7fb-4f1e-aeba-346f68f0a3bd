import { PrismaClient } from '@prisma/client';

export class ProducerApprovalService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  async connect(): Promise<void> {
    // Prisma handles connection automatically
  }

  async disconnect(): Promise<void> {
    await this.prisma.$disconnect();
  }

  // Get pending producer registrations
  async getPendingApprovals(): Promise<any[]> {
    console.log('getPendingApprovals - Starting...');
    
    try {
      const users = await this.prisma.user.findMany({
        where: {
          userType: 'producer',
          status: 'PENDING'
        },
        include: {
          profile: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      console.log(`getPendingApprovals - Found users: ${users.length}`);

      const applications = users.map(user => ({
        id: user.id,
        companyName: user.companyName || '',
        contactPerson: user.profile?.contactPerson || '',
        phone: user.profile?.phone || '',
        email: user.email,
        address: user.profile?.address,
        countryCode: user.profile?.countryCode || '',
        businessDescription: user.profile?.businessDescription || '',
        productionCapacity: user.profile?.productionCapacity || 0,
        certificates: user.profile?.certificates,
        bankInformation: user.profile?.bankInformation,
        status: 'pending' as const,
        submittedAt: user.createdAt,
        documents: this.generateDocumentsFromProfile(user.profile),
        facilities: this.generateFacilitiesFromProfile(user.profile),
        // Ek bilgiler
        companyAddress: user.profile?.companyAddress,
        companyPhone: user.profile?.companyPhone,
        companyEmail: user.profile?.companyEmail,
        website: user.profile?.website,
        foundedYear: user.profile?.foundedYear,
        employeeCount: user.profile?.employeeCount,
        productCategories: user.profile?.productCategories || [],
        certifications: user.profile?.certifications || [],
        hasQuarry: user.profile?.hasQuarry || false,
        quarries: user.profile?.quarries || [],
        factories: user.profile?.factories || [],
        providesCustomManufacturing: user.profile?.offersCustomManufacturing || false,
        customManufacturingDetails: user.profile?.customManufacturingDetails,
        companyDescription: user.profile?.businessDescription
      }));

      console.log(`getPendingApprovals - Returning ${applications.length} applications`);
      return applications;
    } catch (error) {
      console.error('getPendingApprovals - Error:', error);
      throw error;
    }
  }

  // Helper method to generate documents from profile data
  private generateDocumentsFromProfile(profile: any): any[] {
    const documents: any[] = [];

    console.log('Profile data for documents:', {
      userId: profile?.userId,
      verificationDocuments: profile?.verificationDocuments,
      certificates: profile?.certificates
    });

    // Belgeler için farklı kaynaklardan kontrol et
    const docs = profile?.verificationDocuments || {};
    const certs = profile?.certificates || {};

    // Vergi Levhası - her zaman ekle
    const taxCertUrl = docs.taxCertificate || certs.taxCertificate;
    if (taxCertUrl) {
      documents.push({
        id: `tax_cert_${profile?.userId || 'unknown'}`,
        type: 'tax_certificate',
        url: taxCertUrl,
        status: docs.taxCertificateStatus || 'pending',
        verifiedAt: docs.taxCertificateVerifiedAt || null
      });
    }

    // Ticaret Sicil Belgesi - her zaman ekle
    const tradeRegUrl = docs.tradeRegistry || certs.tradeRegistry;
    if (tradeRegUrl) {
      documents.push({
        id: `trade_reg_${profile?.userId || 'unknown'}`,
        type: 'trade_registry',
        url: tradeRegUrl,
        status: docs.tradeRegistryStatus || 'pending',
        verifiedAt: docs.tradeRegistryVerifiedAt || null
      });
    }

    // Maden İşletme Ruhsatı - ocak sahibi ise ekle
    if (profile?.hasQuarry) {
      const miningLicenseUrl = docs.miningLicense || certs.miningLicense;
      if (miningLicenseUrl) {
        documents.push({
          id: `mining_license_${profile?.userId || 'unknown'}`,
          type: 'mining_license',
          url: miningLicenseUrl,
          status: docs.miningLicenseStatus || 'pending',
          verifiedAt: docs.miningLicenseVerifiedAt || null
        });
      }
    }

    // Üretim Kapasite Raporu - her zaman ekle
    const capacityReportUrl = docs.capacityReport || certs.capacityReport;
    if (capacityReportUrl) {
      documents.push({
        id: `capacity_report_${profile?.userId || 'unknown'}`,
        type: 'capacity_report',
        url: capacityReportUrl,
        status: docs.capacityReportStatus || 'pending',
        verifiedAt: docs.capacityReportVerifiedAt || null
      });
    }

    // Sanayi Sicil Belgesi - her zaman ekle
    const industryRegUrl = docs.industryRegistry || certs.industryRegistry;
    if (industryRegUrl) {
      documents.push({
        id: `industry_reg_${profile?.userId || 'unknown'}`,
        type: 'industry_registry',
        url: industryRegUrl,
        status: docs.industryRegistryStatus || 'pending',
        verifiedAt: docs.industryRegistryVerifiedAt || null
      });
    }

    console.log('Generated documents:', documents);
    return documents;
  }

  // Helper method to generate facilities from profile data
  private generateFacilitiesFromProfile(profile: any): any[] {
    const facilities: any[] = [];

    console.log('Profile data for facilities:', {
      userId: profile?.userId,
      quarries: profile?.quarries,
      factories: profile?.factories,
      certificates: profile?.certificates
    });

    // Ana kaynak: Certificates JSON'dan tesis bilgileri al
    if (profile?.certificates) {
      const certs = profile.certificates;

      // Ocak bilgileri
      if (certs.quarries && Array.isArray(certs.quarries)) {
        certs.quarries.forEach((quarry: any, index: number) => {
          facilities.push({
            id: `cert_quarry_${profile?.userId || 'unknown'}_${index}`,
            name: quarry.name || 'Ocak',
            address: quarry.location || quarry.address || '',
            type: 'quarry',
            status: 'verified',
            inspectionDate: profile?.createdAt,
            capacity: quarry.capacity || 'Belirtilmemiş',
            description: quarry.stoneTypes ? `Taş Türleri: ${quarry.stoneTypes.join(', ')}` : (quarry.description || '')
          });
        });
      }

      // Fabrika bilgileri
      if (certs.factories && Array.isArray(certs.factories)) {
        certs.factories.forEach((factory: any, index: number) => {
          facilities.push({
            id: `cert_factory_${profile?.userId || 'unknown'}_${index}`,
            name: factory.name || 'Fabrika',
            address: factory.location || factory.address || '',
            type: 'factory',
            status: 'verified',
            inspectionDate: profile?.createdAt,
            capacity: factory.capacity || 'Belirtilmemiş',
            description: factory.machinery ? `Makineler: ${factory.machinery.join(', ')}` : (factory.description || '')
          });
        });
      }
    }

    // Ek kaynak: Direkt profile alanlarından (eğer varsa)
    if (profile?.quarries && Array.isArray(profile.quarries) && profile.quarries.length > 0) {
      profile.quarries.forEach((quarry: any, index: number) => {
        const existingQuarry = facilities.find(f => f.name === quarry.name && f.type === 'quarry');
        if (!existingQuarry) {
          facilities.push({
            id: `quarry_${profile?.userId || 'unknown'}_${index}`,
            name: quarry.name || 'Ocak',
            address: quarry.address || quarry.location || '',
            type: 'quarry',
            status: 'verified',
            inspectionDate: profile?.createdAt,
            capacity: quarry.capacity || 'Belirtilmemiş',
            description: quarry.description || ''
          });
        }
      });
    }

    if (profile?.factories && Array.isArray(profile.factories) && profile.factories.length > 0) {
      profile.factories.forEach((factory: any, index: number) => {
        const existingFactory = facilities.find(f => f.name === factory.name && f.type === 'factory');
        if (!existingFactory) {
          facilities.push({
            id: `factory_${profile?.userId || 'unknown'}_${index}`,
            name: factory.name || 'Fabrika',
            address: factory.address || factory.location || '',
            type: 'factory',
            status: 'verified',
            inspectionDate: profile?.createdAt,
            capacity: factory.capacity || 'Belirtilmemiş',
            description: factory.description || ''
          });
        }
      });
    }

    // Gerçek tesisler varsa kullan, yoksa boş liste döndür

    console.log('Generated facilities:', facilities);
    return facilities;
  }

  async getApprovedProducers(): Promise<any[]> {
    const users = await this.prisma.user.findMany({
      where: {
        userType: 'producer',
        status: 'ACTIVE'
      },
      include: {
        profile: true
      }
    });

    return users.map(user => ({
      id: user.id,
      companyName: user.companyName,
      email: user.email,
      status: user.status,
      approvedAt: user.profile?.verifiedAt,
      profile: user.profile
    }));
  }

  async approveProducer(applicationId: string, approvalData: any): Promise<void> {
    await this.prisma.user.update({
      where: { id: applicationId },
      data: {
        status: 'ACTIVE',
        profile: {
          update: {
            verificationStatus: 'VERIFIED',
            verifiedAt: new Date(),
            verifiedBy: approvalData.adminId
          }
        }
      }
    });
  }

  async rejectApplication(applicationId: string, reason: string, adminId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: applicationId },
      data: {
        status: 'SUSPENDED',
        profile: {
          update: {
            verificationStatus: 'REJECTED',
            verifiedBy: adminId
          }
        }
      }
    });
  }
}
