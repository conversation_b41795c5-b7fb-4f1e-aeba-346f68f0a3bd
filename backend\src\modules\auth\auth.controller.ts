import { Request, Response, NextFunction } from 'express';
import { async<PERSON>and<PERSON> } from '@/middleware/errorHandler';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { prisma } from '@/database/connection';
import { UserType, UserStatus } from '@prisma/client';
import { tokenService } from '../../services/TokenService';

// Database-based authentication (replacing mock data)

class AuthController {
  register = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    const {
      email,
      password,
      userType,
      companyName,
      countryCode = 'TR', // Default to Turkey
      contactPerson,
      phone,
      // Ek üretici bilgileri
      taxNumber,
      tradeRegistryNumber,
      companyAddress,
      companyPhone,
      companyEmail,
      website,
      foundedYear,
      employeeCount,
      productionCapacity,
      productCategories,
      certifications,
      hasQuarry,
      quarries,
      factories,
      providesCustomManufacturing,
      customManufacturingDetails,
      companyDescription
    } = req.body;

    try {
      // Check if user already exists in database
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          error: 'User with this email already exists'
        });
      }

      // Hash password
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // Convert userType to match Prisma enum
      const userTypeEnum = userType.toLowerCase() as UserType;
      const userStatus = userTypeEnum === 'producer' ? UserStatus.PENDING : UserStatus.ACTIVE;

      // Create new user with profile in database
      const newUser = await prisma.user.create({
        data: {
          email,
          passwordHash,
          userType: userTypeEnum,
          status: userStatus,
          companyName,
          profile: {
            create: {
              companyName,
              countryCode,
              contactPerson,
              phone,
              // Ek üretici bilgileri
              ...(userTypeEnum === 'producer' && {
                taxNumber,
                tradeRegistryNumber,
                companyAddress,
                companyPhone,
                companyEmail,
                website,
                foundedYear,
                employeeCount,
                productionCapacity: productionCapacity ? parseInt(productionCapacity) : null,
                productCategories: productCategories || [],
                businessDescription: companyDescription,
                hasQuarry: hasQuarry === 'true' || hasQuarry === true,
                offersCustomManufacturing: providesCustomManufacturing === 'true' || providesCustomManufacturing === true,
                customManufacturingDetails,
                verificationStatus: 'PENDING',
                certificates: {
                  certifications: certifications || [],
                  quarries: quarries || [],
                  factories: factories || []
                }
              })
            }
          }
        },
        include: {
          profile: true
        }
      });

      // Üretici ise admin onayına düştüğünü belirt
      if (userTypeEnum === 'producer') {
        res.status(201).json({
          success: true,
          message: 'Üretici kaydınız başarıyla alındı. Admin onayı sonrası giriş yapabileceksiniz.',
          data: {
            user: {
              id: newUser.id,
              email: newUser.email,
              userType: newUser.userType,
              status: newUser.status,
              profile: newUser.profile
            },
            requiresApproval: true
          }
        });
      } else {
        // Müşteriler için normal token verme
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
          throw new Error('JWT_SECRET environment variable is required');
        }

        // Generate token pair with refresh token
        const tokenPair = await tokenService.generateTokenPair(
          {
            userId: newUser.id,
            email: newUser.email,
            userType: newUser.userType as 'customer' | 'producer' | 'admin'
          },
          req.get('User-Agent'),
          req.ip
        );

        res.status(201).json({
          success: true,
          message: 'User registered successfully',
          data: {
            user: {
              id: newUser.id,
              email: newUser.email,
              userType: newUser.userType,
              profile: newUser.profile
            },
            accessToken: tokenPair.accessToken,
            refreshToken: tokenPair.refreshToken,
            expiresIn: tokenPair.expiresIn
          }
        });
      }
    } catch (error) {
      console.error('Registration error:', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error during registration'
      });
    }
  });

  login = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    const { email, password, userType } = req.body;

    try {
      // Find user in database with profile
      const user = await prisma.user.findUnique({
        where: { email },
        include: { profile: true }
      });

      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Invalid email or password'
        });
      }

      // Check password
      const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          error: 'Invalid email or password'
        });
      }

      // Kullanıcı tipi kontrolü - daha detaylı log ile
      console.log(`Login attempt - User: ${email}, UserType in DB: ${user.userType}, Requested UserType: ${userType}`);

      if (userType && user.userType.toLowerCase() !== userType.toLowerCase()) {
        console.log(`UserType mismatch - DB: ${user.userType}, Requested: ${userType}`);
        return res.status(403).json({
          success: false,
          error: `Bu giriş sadece ${userType === 'producer' ? 'üreticiler' : 'müşteriler'} içindir. Sizin hesabınız ${user.userType === 'producer' ? 'üretici' : user.userType === 'customer' ? 'müşteri' : 'admin'} hesabıdır. Lütfen doğru giriş formunu kullanın.`
        });
      }

      // Check if producer is approved
      if (user.userType === 'producer' && user.status === 'PENDING') {
        return res.status(403).json({
          success: false,
          error: 'Hesabınız henüz admin onayında. Onay sonrası giriş yapabileceksiniz.',
          requiresApproval: true
        });
      }

      if (user.userType === 'producer' && user.status === 'SUSPENDED') {
        return res.status(403).json({
          success: false,
          error: 'Hesabınız askıya alınmış. Lütfen destek ekibi ile iletişime geçin.'
        });
      }

      // Update last login time
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() }
      });

      // Generate JWT token
      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        throw new Error('JWT_SECRET environment variable is required');
      }

      // Generate token pair with refresh token
      const tokenPair = await tokenService.generateTokenPair(
        {
          userId: user.id,
          email: user.email,
          userType: user.userType as 'customer' | 'producer' | 'admin'
        },
        req.get('User-Agent'),
        req.ip
      );

      res.status(200).json({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            name: user.profile?.companyName || user.companyName || user.email.split('@')[0],
            email: user.email,
            role: user.userType,
            company: user.profile?.companyName || user.companyName
          },
          accessToken: tokenPair.accessToken,
          refreshToken: tokenPair.refreshToken,
          expiresIn: tokenPair.expiresIn
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      return res.status(500).json({
        success: false,
        error: 'Internal server error during login'
      });
    }
  });

  refreshToken = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        error: 'Refresh token is required'
      });
    }

    try {
      const tokenPair = await tokenService.refreshAccessToken(
        refreshToken,
        req.get('User-Agent'),
        req.ip
      );

      res.status(200).json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          accessToken: tokenPair.accessToken,
          refreshToken: tokenPair.refreshToken,
          expiresIn: tokenPair.expiresIn
        }
      });

    } catch (error) {
      console.error('Token refresh error:', error);
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired refresh token'
      });
    }
  });

  logoutUser = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    const { refreshToken } = req.body;

    if (refreshToken) {
      try {
        await tokenService.revokeRefreshToken(refreshToken);
      } catch (error) {
        console.error('Token revocation error:', error);
        // Don't fail logout if token revocation fails
      }
    }

    res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
  });



  verifyEmail = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    // TODO: Implement email verification
    res.status(200).json({
      success: true,
      message: 'Email verification endpoint - Coming soon'
    });
  });

  forgotPassword = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    // TODO: Implement forgot password
    res.status(200).json({
      success: true,
      message: 'Forgot password endpoint - Coming soon'
    });
  });

  resetPassword = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
    // TODO: Implement password reset
    res.status(200).json({
      success: true,
      message: 'Password reset endpoint - Coming soon'
    });
  });
}

export const authController = new AuthController();
export default AuthController;
