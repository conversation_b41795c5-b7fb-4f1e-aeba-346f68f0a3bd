// RFC-501: Producer Approval Dashboard Component
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  MapPin,
  Building,
  Building2,
  Factory,
  Phone,
  Mail,
  Calendar,
  AlertTriangle
} from 'lucide-react';

interface ProducerRegistration {
  id: string;
  companyName: string;
  companyType: string;
  contactPerson: string;
  phone: string;
  countryCode: string;
  businessDescription: string;
  productionCapacity: number;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: Date;
  documents: DocumentVerification[];
  facilities: FacilityInspection[];
  // Ek bilgiler
  email?: string;
  taxNumber?: string;
  tradeRegistryNumber?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
  website?: string;
  foundedYear?: string;
  employeeCount?: string;
  productCategories?: string[];
  certifications?: string[];
  hasQuarry?: boolean;
  quarries?: Array<{
    name: string;
    address: string;
    googleMapsLink?: string;
    description?: string;
  }>;
  factories?: Array<{
    name: string;
    address: string;
    googleMapsLink?: string;
    capacity: string;
    description?: string;
  }>;
  providesCustomManufacturing?: boolean;
  customManufacturingDetails?: string;
  companyDescription?: string;
}

interface DocumentVerification {
  id: string;
  type: string;
  url: string;
  status: 'pending' | 'verified' | 'rejected';
  verifiedAt?: Date;
}

interface FacilityInspection {
  id: string;
  type: 'quarry' | 'factory';
  name: string;
  address: string;
  status: 'pending' | 'scheduled' | 'completed' | 'approved' | 'rejected';
  scheduledDate?: Date;
  inspectionDate?: Date;
}

export default function ProducerApprovalDashboard() {
  const [pendingApprovals, setPendingApprovals] = useState<ProducerRegistration[]>([]);
  const [selectedApplication, setSelectedApplication] = useState<ProducerRegistration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch pending approvals from API
  useEffect(() => {
    fetchPendingApprovals();
  }, []);

  const fetchPendingApprovals = async () => {
    setIsLoading(true);
    try {
      // Next.js API route üzerinden backend'e istek yap
      const response = await fetch('/api/admin/producers/pending', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setPendingApprovals(result.data || []);
        } else {
          console.error('Failed to fetch pending approvals:', result.message);
          setPendingApprovals([]);
        }
      } else {
        const errorText = await response.text();
        console.error('API request failed:', response.status, response.statusText, errorText);
        setPendingApprovals([]);
      }
    } catch (error) {
      console.error('Error fetching pending approvals:', error);
      setPendingApprovals([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (applicationId: string, notes: string) => {
    setActionLoading(true);
    try {
      // Next.js API route üzerinden backend'e istek yap
      const response = await fetch(`/api/admin/producers/${applicationId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes,
          conditions: [] // Add any conditions if needed
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Update local state
          setPendingApprovals(prev =>
            prev.filter(app => app.id !== applicationId)
          );
          setSelectedApplication(null);
          alert('Üretici başarıyla onaylandı!');
        } else {
          console.error('Failed to approve application:', result.message);
          alert('Onaylama işlemi başarısız: ' + result.message);
        }
      } else {
        const errorText = await response.text();
        console.error('API request failed:', response.status, response.statusText, errorText);
        alert('Onaylama işlemi sırasında bir hata oluştu.');
      }
    } catch (error) {
      console.error('Error approving application:', error);
      alert('Onaylama işlemi sırasında bir hata oluştu.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = async (applicationId: string, reason: string) => {
    setActionLoading(true);
    try {
      // Next.js API route üzerinden backend'e istek yap
      const response = await fetch(`/api/admin/producers/${applicationId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // Update local state
          setPendingApprovals(prev =>
            prev.filter(app => app.id !== applicationId)
          );
          setSelectedApplication(null);
          alert('Üretici başvurusu reddedildi.');
        } else {
          console.error('Failed to reject application:', result.message);
          alert('Reddetme işlemi başarısız: ' + result.message);
        }
      } else {
        const errorText = await response.text();
        console.error('API request failed:', response.status, response.statusText, errorText);
        alert('Reddetme işlemi sırasında bir hata oluştu.');
      }
    } catch (error) {
      console.error('Error rejecting application:', error);
      alert('Reddetme işlemi sırasında bir hata oluştu.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDocumentApproval = async (documentId: string, status: 'verified' | 'rejected') => {
    try {
      // Belge durumunu güncelle
      console.log(`Belge ${documentId} durumu ${status} olarak güncellendi`);

      // API çağrısı yap
      const response = await fetch(`/api/admin/documents/${documentId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status,
          adminId: 'admin' // Gerçek admin ID'si buraya gelecek
        })
      });

      if (response.ok) {
        // Listeyi yenile ama modal'ı açık tut
        await fetchPendingApprovals();

        // Seçili uygulamayı güncelle
        if (selectedApplication) {
          // Güncellenmiş veriyi al
          const response = await fetch('/api/admin/producers/pending');
          if (response.ok) {
            const data = await response.json();
            const updatedApp = data.data.find((app: any) => app.id === selectedApplication.id);
            if (updatedApp) {
              setSelectedApplication(updatedApp);
            }
          }
        }

        const message = status === 'verified' ? 'Belge onaylandı!' : 'Belge reddedildi!';
        alert(message);
      } else {
        throw new Error('API request failed');
      }
    } catch (error) {
      console.error('Error updating document status:', error);
      alert('Belge durumu güncellenirken hata oluştu.');
    }
  };

  const handleDocumentRejection = async (documentId: string) => {
    const reason = prompt('Belgeyi reddetme sebebinizi belirtiniz:');
    if (reason && reason.trim().length > 0) {
      try {
        // API çağrısı yap
        const response = await fetch(`/api/admin/documents/${documentId}/status`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: 'rejected',
            adminId: 'admin', // Gerçek admin ID'si buraya gelecek
            reason: reason
          })
        });

        if (response.ok) {
          // Listeyi yenile ama modal'ı açık tut
          await fetchPendingApprovals();

          // Seçili uygulamayı güncelle
          if (selectedApplication) {
            const response = await fetch('/api/admin/producers/pending');
            if (response.ok) {
              const data = await response.json();
              const updatedApp = data.data.find((app: any) => app.id === selectedApplication.id);
              if (updatedApp) {
                setSelectedApplication(updatedApp);
              }
            }
          }

          alert('Belge reddedildi!');
        } else {
          throw new Error('API request failed');
        }
      } catch (error) {
        console.error('Error rejecting document:', error);
        alert('Belge reddedilirken hata oluştu.');
      }
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Beklemede</Badge>;
      case 'verified':
      case 'approved':
        return <Badge variant="default"><CheckCircle className="w-3 h-3 mr-1" />Onaylandı</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Reddedildi</Badge>;
      case 'scheduled':
        return <Badge variant="outline"><Calendar className="w-3 h-3 mr-1" />Planlandı</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getDocumentTypeName = (type: string) => {
    switch (type) {
      case 'tax_certificate':
        return 'Vergi Levhası';
      case 'trade_registry':
        return 'Ticaret Sicil Belgesi';
      case 'mining_license':
        return 'Maden İşletme Ruhsatı';
      case 'capacity_report':
        return 'Üretim Kapasite Raporu';
      case 'industry_registry':
        return 'Sanayi Sicil Belgesi';
      case 'iso_certificate':
        return 'ISO Sertifikası';
      case 'bank_statement':
        return 'Banka Bilgileri';
      default:
        return type.replace('_', ' ');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading applications...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Üretici Onayları</h1>
          <p className="text-muted-foreground">
            Üretici kayıtlarını inceleyin ve onaylayın
          </p>
          <div className="mt-2 text-sm text-blue-600 bg-blue-50 p-2 rounded">
            💡 <strong>Kullanım:</strong> İncele butonuna tıklayarak başvuru detaylarını görüntüleyin, belgeleri kontrol edin ve karar verin.
          </div>
        </div>
        <Badge variant="outline" className="text-lg px-3 py-1">
          {pendingApprovals.length} Beklemede
        </Badge>
      </div>

      {/* Applications List */}
      <div className="grid gap-4">
        {pendingApprovals.map((application) => (
          <Card key={application.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center space-x-3">
                    <Building className="w-5 h-5 text-blue-600" />
                    <h3 className="text-lg font-semibold">{application.companyName}</h3>
                    {getStatusBadge(application.status)}
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4" />
                      <span>{application.contactPerson}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4" />
                      <span>{application.countryCode}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>Applied: {new Date(application.submittedAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Building className="w-4 h-4" />
                      <span>Capacity: {application.productionCapacity.toLocaleString()} m³/year</span>
                    </div>
                  </div>

                  <p className="text-sm">{application.businessDescription}</p>

                  {/* Document Status */}
                  <div className="flex flex-wrap gap-2 mt-3">
                    {application.documents.map((doc) => (
                      <div key={doc.id} className="flex items-center space-x-1">
                        <FileText className="w-3 h-3" />
                        <span className="text-xs">{getDocumentTypeName(doc.type)}</span>
                        {getStatusBadge(doc.status)}
                      </div>
                    ))}
                  </div>
                </div>

                <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSelectedApplication(application);
                        setIsModalOpen(true);
                      }}
                    >
                      İncele
                    </Button>
                  </DialogTrigger>

                  <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>Başvuru İnceleme: {application.companyName}</DialogTitle>
                    </DialogHeader>

                    {selectedApplication && (
                      <ApplicationReviewModal
                        application={selectedApplication}
                        onApprove={handleApprove}
                        onReject={handleReject}
                        isLoading={actionLoading}
                        onDocumentApproval={handleDocumentApproval}
                        onDocumentRejection={handleDocumentRejection}
                      />
                    )}
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {pendingApprovals.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Hepsi Tamamlandı!</h3>
            <p className="text-muted-foreground">İncelenecek bekleyen üretici başvurusu yok.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Application Review Modal Component
function ApplicationReviewModal({
  application,
  onApprove,
  onReject,
  isLoading,
  onDocumentApproval,
  onDocumentRejection
}: {
  application: ProducerRegistration;
  onApprove: (id: string, notes: string) => void;
  onReject: (id: string, reason: string) => void;
  isLoading: boolean;
  onDocumentApproval: (documentId: string, status: 'verified' | 'rejected') => void;
  onDocumentRejection: (documentId: string) => void;
}) {
  const [notes, setNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');

  // Status badge function for modal
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Beklemede</Badge>;
      case 'verified':
      case 'approved':
        return <Badge variant="default"><CheckCircle className="w-3 h-3 mr-1" />Onaylandı</Badge>;
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Reddedildi</Badge>;
      default:
        return <Badge variant="outline"><AlertTriangle className="w-3 h-3 mr-1" />Bilinmiyor</Badge>;
    }
  };

  const getDocumentTypeName = (type: string) => {
    switch (type) {
      case 'tax_certificate':
        return 'Vergi Levhası'
      case 'trade_registry':
        return 'Ticaret Sicil Belgesi'
      case 'mining_license':
        return 'Maden İşletme Ruhsatı'
      case 'capacity_report':
        return 'Üretim Kapasite Raporu'
      case 'industry_registry':
        return 'Sanayi Sicil Belgesi'
      case 'iso_certificate':
        return 'ISO Sertifikası'
      case 'bank_statement':
        return 'Banka Bilgileri'
      default:
        return type.replace('_', ' ')
    }
  };

  return (
    <Tabs defaultValue="details" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="details" className="flex items-center space-x-2">
          <Building className="w-4 h-4" />
          <span>Şirket Detayları</span>
        </TabsTrigger>
        <TabsTrigger value="documents" className="flex items-center space-x-2">
          <FileText className="w-4 h-4" />
          <span>Belgeler ({application.documents.length})</span>
        </TabsTrigger>
        <TabsTrigger value="facilities" className="flex items-center space-x-2">
          <MapPin className="w-4 h-4" />
          <span>Tesisler ({application.facilities.length})</span>
        </TabsTrigger>
      </TabsList>
      
      <TabsContent value="details" className="space-y-4">
        <div className="space-y-6">
          {/* Temel Bilgiler */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 text-blue-900">Temel Şirket Bilgileri</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-blue-700">Şirket Adı</Label>
                <p className="font-medium text-lg">{application.companyName}</p>
              </div>
              <div>
                <Label className="text-blue-700">Şirket Türü</Label>
                <p className="font-medium">{application.companyType}</p>
              </div>
              <div>
                <Label className="text-blue-700">İletişim Kişisi</Label>
                <p className="font-medium">{application.contactPerson}</p>
              </div>
              <div>
                <Label className="text-blue-700">Telefon</Label>
                <p className="font-medium">{application.phone}</p>
              </div>
              {application.email && (
                <div>
                  <Label className="text-blue-700">E-posta</Label>
                  <p className="font-medium">{application.email}</p>
                </div>
              )}
              {application.taxNumber && (
                <div>
                  <Label className="text-blue-700">Vergi Numarası</Label>
                  <p className="font-medium">{application.taxNumber}</p>
                </div>
              )}
              {application.tradeRegistryNumber && (
                <div>
                  <Label className="text-blue-700">Ticaret Sicil No</Label>
                  <p className="font-medium">{application.tradeRegistryNumber}</p>
                </div>
              )}
              {application.companyPhone && (
                <div>
                  <Label className="text-blue-700">Şirket Telefonu</Label>
                  <p className="font-medium">{application.companyPhone}</p>
                </div>
              )}
              {application.companyEmail && (
                <div>
                  <Label className="text-blue-700">Şirket E-postası</Label>
                  <p className="font-medium">{application.companyEmail}</p>
                </div>
              )}
              {application.website && (
                <div>
                  <Label className="text-blue-700">Web Sitesi</Label>
                  <p className="font-medium">
                    <a href={application.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                      {application.website}
                    </a>
                  </p>
                </div>
              )}
              {application.foundedYear && (
                <div>
                  <Label className="text-blue-700">Kuruluş Yılı</Label>
                  <p className="font-medium">{application.foundedYear}</p>
                </div>
              )}
              {application.employeeCount && (
                <div>
                  <Label className="text-blue-700">Çalışan Sayısı</Label>
                  <p className="font-medium">{application.employeeCount}</p>
                </div>
              )}
            </div>
          </div>

          {/* Şirket Adresi */}
          {application.companyAddress && (
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="font-semibold text-lg mb-3 text-purple-900">Şirket Adresi</h3>
              <p className="font-medium text-purple-800">{application.companyAddress}</p>
            </div>
          )}

          {/* Üretim Bilgileri */}
          <div className="bg-orange-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 text-orange-900">Üretim Bilgileri</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-orange-700">Üretim Kapasitesi</Label>
                <p className="font-medium">{application.productionCapacity} m²/ay</p>
              </div>
              {application.productCategories && application.productCategories.length > 0 && (
                <div>
                  <Label className="text-orange-700">Ürün Kategorileri</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {application.productCategories.map((category, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {category}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              {application.certifications && application.certifications.length > 0 && (
                <div className="col-span-2">
                  <Label className="text-orange-700">Sertifikalar</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {application.certifications.map((cert, index) => (
                      <Badge key={index} variant="outline" className="text-xs bg-green-100 text-green-800">
                        {cert}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              {application.providesCustomManufacturing && (
                <div className="col-span-2">
                  <Label className="text-orange-700">Fason Üretim</Label>
                  <p className="font-medium text-green-600">✅ Fason üretim yapıyor</p>
                  {application.customManufacturingDetails && (
                    <p className="text-sm text-gray-600 mt-1">{application.customManufacturingDetails}</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* İş Açıklaması */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 text-green-900">İş Açıklaması</h3>
            <p className="font-medium text-green-800 leading-relaxed">{application.businessDescription}</p>
          </div>

          {/* Başvuru Bilgileri */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 text-gray-900">Başvuru Detayları</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-gray-700">Başvuru Tarihi</Label>
                <p className="font-medium">{new Date(application.submittedAt).toLocaleDateString('tr-TR')}</p>
              </div>
              <div>
                <Label className="text-gray-700">Ülke Kodu</Label>
                <p className="font-medium">{application.countryCode}</p>
              </div>
              <div>
                <Label className="text-gray-700">Üretim Kapasitesi</Label>
                <p className="font-medium">{application.productionCapacity} m²/ay</p>
              </div>
              <div>
                <Label className="text-gray-700">Mevcut Durum</Label>
                {getStatusBadge(application.status)}
              </div>
            </div>
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="documents" className="space-y-4">
        {application.documents.map((doc) => (
          <div key={doc.id} className="flex items-center justify-between p-3 border rounded hover:bg-gray-50">
            <div className="flex items-center space-x-3 flex-1">
              <FileText className="w-5 h-5 text-blue-600" />
              <div className="flex-1">
                <p className="font-medium">{getDocumentTypeName(doc.type)}</p>
                {doc.url ? (
                  <a
                    href={doc.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 underline"
                  >
                    Belgeyi Görüntüle
                  </a>
                ) : (
                  <span className="text-sm text-gray-500">
                    Belge henüz yüklenmemiş
                  </span>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  {doc.verifiedAt ? `Doğrulandı: ${new Date(doc.verifiedAt).toLocaleDateString('tr-TR')}` : 'Henüz doğrulanmadı'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusBadge(doc.status)}
              {doc.status === 'pending' && (
                <div className="flex space-x-1">
                  <Button
                    size="sm"
                    variant="outline"
                    className="text-green-600 hover:text-green-700 border-green-300 hover:border-green-400"
                    onClick={() => onDocumentApproval(doc.id, 'verified')}
                  >
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Onayla
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                    onClick={() => onDocumentRejection(doc.id)}
                  >
                    <XCircle className="w-3 h-3 mr-1" />
                    Reddet
                  </Button>
                </div>
              )}
            </div>
          </div>
        ))}
        {application.documents.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>Henüz belge yüklenmemiş</p>
          </div>
        )}
      </TabsContent>

      <TabsContent value="facilities" className="space-y-6">
        {/* Ocak Bilgileri */}
        {application.hasQuarry && application.quarries && application.quarries.length > 0 && (
          <div className="bg-amber-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 text-amber-900 flex items-center">
              <Building2 className="w-5 h-5 mr-2" />
              Ocaklar ({application.quarries.length})
            </h3>
            <div className="space-y-3">
              {application.quarries.map((quarry, index) => (
                <div key={index} className="bg-white p-3 rounded border">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-lg">{quarry.name}</h4>
                      <p className="text-sm text-gray-600 flex items-center mt-1">
                        <MapPin className="w-3 h-3 mr-1" />
                        {quarry.address}
                      </p>
                      {quarry.description && (
                        <p className="text-sm text-gray-700 mt-2">{quarry.description}</p>
                      )}
                      {quarry.googleMapsLink && (
                        <a
                          href={quarry.googleMapsLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:underline mt-1 inline-block"
                        >
                          📍 Google Maps'te Görüntüle
                        </a>
                      )}
                    </div>
                    <Badge variant="outline" className="bg-green-100 text-green-800">
                      Ocak
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Fabrika Bilgileri */}
        {application.factories && application.factories.length > 0 && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 text-blue-900 flex items-center">
              <Factory className="w-5 h-5 mr-2" />
              Fabrikalar ({application.factories.length})
            </h3>
            <div className="space-y-3">
              {application.factories.map((factory, index) => (
                <div key={index} className="bg-white p-3 rounded border">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-lg">{factory.name}</h4>
                      <p className="text-sm text-gray-600 flex items-center mt-1">
                        <MapPin className="w-3 h-3 mr-1" />
                        {factory.address}
                      </p>
                      <div className="flex items-center gap-4 mt-2">
                        <span className="text-sm text-gray-700">
                          <strong>Kapasite:</strong> {factory.capacity}
                        </span>
                      </div>
                      {factory.description && (
                        <p className="text-sm text-gray-700 mt-2">{factory.description}</p>
                      )}
                      {factory.googleMapsLink && (
                        <a
                          href={factory.googleMapsLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:underline mt-1 inline-block"
                        >
                          📍 Google Maps'te Görüntüle
                        </a>
                      )}
                    </div>
                    <Badge variant="outline" className="bg-blue-100 text-blue-800">
                      Fabrika
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Facilities Array'den Gelen Tesisler (Ana Kaynak) */}
        {application.facilities && application.facilities.length > 0 && (
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 text-green-900 flex items-center">
              <Building className="w-5 h-5 mr-2" />
              Kayıtlı Tesisler ({application.facilities.length})
            </h3>
            <div className="space-y-3">
              {application.facilities.map((facility, index) => (
                <div key={facility.id || index} className="bg-white p-3 rounded border">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-medium text-lg">{facility.name}</h4>
                        <Badge variant={facility.type === 'quarry' ? 'default' : 'secondary'}>
                          {facility.type === 'quarry' ? 'Ocak' : 'Fabrika'}
                        </Badge>
                        {getStatusBadge(facility.status)}
                      </div>
                      <p className="text-sm text-gray-600 flex items-center">
                        <MapPin className="w-3 h-3 mr-1" />
                        {facility.address}
                      </p>
                      {facility.capacity && (
                        <p className="text-sm text-gray-600 mt-1">
                          <strong>Kapasite:</strong> {facility.capacity}
                        </p>
                      )}
                      {facility.description && (
                        <p className="text-sm text-gray-500 mt-2">{facility.description}</p>
                      )}
                      {facility.inspectionDate && (
                        <p className="text-xs text-gray-400 mt-2">
                          İnceleme Tarihi: {new Date(facility.inspectionDate).toLocaleDateString('tr-TR')}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Hiç tesis yoksa */}
        {(!application.quarries || application.quarries.length === 0) &&
         (!application.factories || application.factories.length === 0) &&
         (!application.facilities || application.facilities.length === 0) && (
          <div className="text-center py-8 text-muted-foreground">
            <MapPin className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>Henüz tesis bilgisi eklenmemiş</p>
          </div>
        )}
      </TabsContent>

      {/* İnceleme Özeti */}
      <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <h3 className="font-semibold text-lg mb-3 text-yellow-900">İnceleme Özeti</h3>
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <p className="font-medium text-yellow-700">Toplam Belge</p>
            <p className="text-2xl font-bold text-yellow-900">{application.documents.length}</p>
          </div>
          <div className="text-center">
            <p className="font-medium text-yellow-700">Onaylanan Belge</p>
            <p className="text-2xl font-bold text-green-600">
              {application.documents.filter(doc => doc.status === 'verified').length}
            </p>
          </div>
          <div className="text-center">
            <p className="font-medium text-yellow-700">Toplam Tesis</p>
            <p className="text-2xl font-bold text-yellow-900">
              {(application.facilities?.length || 0) +
               (application.quarries?.length || 0) +
               (application.factories?.length || 0)}
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white p-6 border-t-2 border-gray-200">
        <div className="space-y-4">
          <div>
            <Label htmlFor="notes" className="text-lg font-semibold">Onay Notları</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Onay için notlarınızı, koşullarınızı veya özel talimatlarınızı ekleyin..."
              rows={4}
              className="mt-2"
            />
          </div>

          <div className="flex space-x-4">
            <Button
              onClick={() => onApprove(application.id, notes)}
              disabled={isLoading}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 text-lg"
            >
              <CheckCircle className="w-5 h-5 mr-2" />
              {isLoading ? 'Onaylanıyor...' : 'Başvuruyu Onayla'}
            </Button>

            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="destructive"
                  disabled={isLoading}
                  className="flex-1 py-3 text-lg"
                >
                  <XCircle className="w-5 h-5 mr-2" />
                  Başvuruyu Reddet
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-red-700">Başvuruyu Reddet</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="bg-red-50 p-3 rounded border border-red-200">
                    <p className="text-sm text-red-700">
                      ⚠️ Bu işlem geri alınamaz. Başvuru reddedildikten sonra üretici bilgilendirilecektir.
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="reason" className="font-semibold">Ret Sebebi *</Label>
                    <Textarea
                      id="reason"
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      placeholder="Başvurunun neden reddedildiğini detaylı olarak açıklayın. Bu bilgi üreticiye iletilecektir..."
                      rows={5}
                      className="mt-2"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Minimum 20 karakter gerekli ({rejectionReason.length}/20)
                    </p>
                  </div>

                  <div className="flex space-x-2">
                    <DialogTrigger asChild>
                      <Button variant="outline" className="flex-1">
                        İptal
                      </Button>
                    </DialogTrigger>
                    <Button
                      onClick={() => onReject(application.id, rejectionReason)}
                      disabled={isLoading || rejectionReason.trim().length < 20}
                      variant="destructive"
                      className="flex-1"
                    >
                      {isLoading ? 'Reddediliyor...' : 'Reddet'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </Tabs>
  );
}
