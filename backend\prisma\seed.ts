import { PrismaClient, UserType, UserStatus } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Hash password for all test users
  const saltRounds = 12;
  const passwordHash = await bcrypt.hash('password', saltRounds);

  // Create test customer
  const customer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash,
      userType: UserType.customer,
      status: UserStatus.ACTIVE,
      companyName: 'Test Müşteri Şirketi',
      emailVerified: true,
      profile: {
        create: {
          companyName: 'Test Müşteri Şirketi',
          countryCode: 'TR',
          contactPerson: 'Test Kullanıcı',
          phone: '+90 ************',
          address: {
            street: 'Test Caddesi No:123',
            city: 'İstanbul',
            country: 'Türkiye',
            postalCode: '34000'
          },
          website: 'https://testmusteri.com'
        }
      }
    },
    include: {
      profile: true
    }
  });

  // Create test producer
  const producer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash,
      userType: UserType.producer,
      status: UserStatus.ACTIVE, // Approved for testing
      companyName: 'Test Üretici Şirketi',
      emailVerified: true,
      profile: {
        create: {
          companyName: 'Test Üretici Şirketi',
          countryCode: 'TR',
          contactPerson: 'Test Üretici',
          phone: '+90 ************',
          address: {
            street: 'Organize Sanayi Bölgesi 1. Cadde No:45',
            city: 'Afyon',
            country: 'Türkiye',
            postalCode: '03000'
          },
          taxNumber: '1234567890',
          tradeRegistryNumber: 'TR-123456',
          website: 'https://testuretici.com',
          productionCapacity: 1000, // Integer value for m³/month
          businessDescription: 'Türkiye\'nin önde gelen doğal taş üreticilerinden biri',
          offersCustomManufacturing: true,
          customManufacturingDetails: 'Özel ölçü ve tasarım üretimi yapılmaktadır',
          certificates: {
            certifications: ['ISO 9001', 'CE Belgesi'],
            quarries: [
              {
                name: 'Afyon Beyaz Mermer Ocağı',
                location: 'Afyon/İscehisar',
                capacity: '500 m³/ay',
                stoneTypes: ['Afyon Beyaz']
              }
            ],
            factories: [
              {
                name: 'Ana Üretim Tesisi',
                location: 'Afyon Organize Sanayi Bölgesi',
                capacity: '1000 m³/ay',
                machinery: ['Köprü Kesim', 'Cilalama', 'Kalibrasyon']
              }
            ],
            // Gerçek belge URL'leri
            taxCertificate: '/uploads/documents/tax-certificate-1.pdf',
            tradeRegistry: '/uploads/documents/trade-registry-1.pdf',
            capacityReport: '/uploads/documents/capacity-report-1.pdf',
            industryRegistry: '/uploads/documents/industry-registry-1.pdf'
          }
        }
      }
    },
    include: {
      profile: true
    }
  });

  // Create test admin
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash,
      userType: UserType.admin,
      status: UserStatus.ACTIVE,
      companyName: 'Platform Yönetimi',
      emailVerified: true,
      profile: {
        create: {
          companyName: 'Platform Yönetimi',
          countryCode: 'TR',
          contactPerson: 'Admin Kullanıcı',
          phone: '+90 ************',
          address: {
            street: 'Admin Caddesi No:1',
            city: 'İstanbul',
            country: 'Türkiye',
            postalCode: '34000'
          },
          website: 'https://naturalstone-marketplace.com'
        }
      }
    },
    include: {
      profile: true
    }
  });

  // Create additional test producer (pending approval)
  const pendingProducer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      passwordHash,
      userType: UserType.producer,
      status: UserStatus.PENDING, // Waiting for admin approval
      companyName: 'Bekleyen Üretici Şirketi',
      emailVerified: true,
      profile: {
        create: {
          companyName: 'Bekleyen Üretici Şirketi',
          countryCode: 'TR',
          contactPerson: 'Bekleyen Üretici',
          phone: '+90 ************',
          address: {
            street: 'Sanayi Caddesi No:67',
            city: 'Denizli',
            country: 'Türkiye',
            postalCode: '20000'
          },
          taxNumber: '9876543210',
          tradeRegistryNumber: 'TR-654321',
          website: 'https://bekleyenuretici.com',
          productionCapacity: 500, // Integer value for m³/month
          businessDescription: 'Traverten ve oniks konusunda uzman üretici',
          offersCustomManufacturing: false,
          certificates: {
            certifications: ['ISO 14001'],
            quarries: [
              {
                name: 'Denizli Traverten Ocağı',
                location: 'Denizli/Pamukkale',
                capacity: '300 m³/ay',
                stoneTypes: ['Denizli Traverten']
              }
            ],
            factories: [
              {
                name: 'Traverten İşleme Tesisi',
                location: 'Denizli Organize Sanayi Bölgesi',
                capacity: '500 m³/ay',
                machinery: ['Traverten Kesim', 'Yüzey İşleme']
              }
            ],
            // Gerçek belge URL'leri
            taxCertificate: '/uploads/documents/tax-certificate-2.pdf',
            tradeRegistry: '/uploads/documents/trade-registry-2.pdf',
            capacityReport: '/uploads/documents/capacity-report-2.pdf',
            industryRegistry: '/uploads/documents/industry-registry-2.pdf'
          }
        }
      }
    },
    include: {
      profile: true
    }
  });

  console.log('✅ Database seeding completed successfully!');
  console.log('📊 Created users:');
  console.log(`   👤 Customer: ${customer.email} (ID: ${customer.id})`);
  console.log(`   🏭 Producer: ${producer.email} (ID: ${producer.id})`);
  console.log(`   👨‍💼 Admin: ${admin.email} (ID: ${admin.id})`);
  console.log(`   ⏳ Pending Producer: ${pendingProducer.email} (ID: ${pendingProducer.id})`);
  console.log('🔑 Default password for all users: "password"');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
